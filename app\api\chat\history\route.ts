import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { dbService } from '@/lib/db-service'
import { memoryService } from '@/lib/ai/memory-service'

/**
 * GET /api/chat/history
 * Restore chat history for a document
 */
export async function GET(request: NextRequest) {
  try {
    // Authenticate user
    const supabase = await createClient()
    const {
      data: { session },
    } = await supabase.auth.getSession()

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get document ID from query params
    const { searchParams } = new URL(request.url)
    const documentId = searchParams.get('documentId')

    if (!documentId) {
      return NextResponse.json(
        { error: 'Document ID is required' },
        { status: 400 }
      )
    }

    // Verify document belongs to user
    const document = await dbService.document.findFirst({
      where: {
        id: documentId,
        userId: session.user.id
      }
    })

    if (!document) {
      return NextResponse.json(
        { error: 'Document not found or access denied' },
        { status: 404 }
      )
    }

    // Get user's progress to find session ID and current chunk
    const progress = await dbService.progress.findUnique({
      where: {
        userId_documentId: {
          userId: session.user.id,
          documentId
        }
      }
    })

    if (!progress?.sessionId) {
      return NextResponse.json({
        messages: [],
        currentChunk: 0,
        totalChunks: document.totalChunks,
        sessionId: null
      })
    }

    // Try to get conversation history from Mem0
    let messages: any[] = []
    try {
      console.log(`📚 Retrieving chat history for session: ${progress.sessionId}`)
      
      // Get all memories for this session
      const memories = await memoryService.getAllMemories(progress.sessionId)
      
      // Convert memories to message format
      messages = memories
        .filter(memory => memory.metadata?.type === 'chat_message')
        .sort((a, b) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime())
        .map(memory => ({
          id: memory.id,
          role: memory.metadata?.role || 'assistant',
          content: memory.memory || memory.text,
          timestamp: memory.created_at
        }))

      console.log(`✅ Retrieved ${messages.length} messages from chat history`)
    } catch (error) {
      console.error('⚠️ Failed to retrieve chat history from Mem0:', error)
      // Continue with empty messages - don't block the user
    }

    return NextResponse.json({
      messages,
      currentChunk: progress.currentChunk,
      totalChunks: document.totalChunks,
      sessionId: progress.sessionId,
      documentName: document.fileName
    })

  } catch (error) {
    console.error('Chat history API error:', error)
    return NextResponse.json(
      {
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
