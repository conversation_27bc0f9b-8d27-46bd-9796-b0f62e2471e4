// Simple TypeScript demo
function greetUser(user) {
    return `Hello, ${user.name}! You are ${user.age} years old.`;
}
const user = {
    name: "<PERSON>",
    age: 30,
    email: "<EMAIL>"
};
console.log(greetUser(user));
// Let's also demonstrate some other TypeScript features
const numbers = [1, 2, 3, 4, 5];
const doubled = numbers.map((num) => num * 2);
console.log("Doubled numbers:", doubled);
