import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { dbService } from '@/lib/db-service'
import { memoryService } from '@/lib/ai/memory-service'

/**
 * Chat Initialization API Endpoint
 * Handles chat session initialization and restoration
 */

interface InitializeChatRequest {
  documentId: string
  chunkIndex?: number
}

/**
 * POST /api/chat/initialize
 * Initialize or restore chat session for a document
 */
export async function POST(request: NextRequest) {
  try {
    // Authenticate user
    const supabase = await createClient()
    const {
      data: { session },
    } = await supabase.auth.getSession()

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Parse request body
    const {
      documentId,
      chunkIndex = 0
    }: InitializeChatRequest = await request.json()

    // Validate required fields
    if (!documentId) {
      return NextResponse.json(
        { error: 'Document ID is required' },
        { status: 400 }
      )
    }

    // Verify document exists and belongs to user
    const document = await dbService.document.findFirst({
      where: {
        id: documentId,
        userId: session.user.id
      }
    })

    if (!document) {
      return NextResponse.json(
        { error: 'Document not found or access denied' },
        { status: 404 }
      )
    }

    // Verify document is ready
    if (document.status !== 'READY') {
      return NextResponse.json(
        {
          error: 'Document is not ready for chat',
          status: document.status
        },
        { status: 400 }
      )
    }

    // Validate chunk index
    if (chunkIndex < 0 || chunkIndex >= document.totalChunks) {
      return NextResponse.json(
        { error: 'Invalid chunk index' },
        { status: 400 }
      )
    }

    // Get the requested chunk
    const chunk = await dbService.chunk.findUnique({
      where: {
        documentId_chunkIndex: {
          documentId,
          chunkIndex
        }
      }
    })

    if (!chunk) {
      return NextResponse.json(
        { error: 'Chunk not found' },
        { status: 404 }
      )
    }

    // Get or create progress record
    let progress = await dbService.progress.upsert({
      where: {
        userId_documentId: {
          userId: session.user.id,
          documentId
        }
      },
      update: {
        currentChunk: chunkIndex,
        updatedAt: new Date()
      },
      create: {
        userId: session.user.id,
        documentId,
        currentChunk: chunkIndex,
        sessionId: `${session.user.id}-${documentId}-${Date.now()}`
      }
    })

    // Initialize conversation history
    let conversationHistory: any[] = []

    // Try to restore conversation history from document-specific memory
    try {
      console.log(`🔍 Attempting to retrieve memories for document: ${documentId}, user: ${session.user.id}`)

      // Get conversation history from Mem0 using document-based storage
      const memories = await memoryService.getAllMemories(documentId, session.user.id)
      console.log(`🔍 Retrieved ${memories.length} conversation memories from Mem0`)

      if (memories.length > 0) {
        // Format existing conversation history
        conversationHistory = memories
          .slice(-20) // Get last 20 messages
          .map((memory: any, index: number) => ({
            id: memory.id || `msg_${index}`,
            role: memory.metadata?.role || 'assistant',
            content: memory.memory || memory.text || memory.content,
            timestamp: memory.metadata?.timestamp || memory.created_at
          }))

        console.log(`📚 Restored ${conversationHistory.length} messages from existing session`)
      }
    } catch (error) {
      console.warn('⚠️ Failed to restore conversation history:', error)
      // Continue with empty history - don't block the user
    }

    // If no conversation history found, this is a new session - just display the chunk
    if (conversationHistory.length === 0) {
      console.log('🔄 No conversation history found, displaying first chunk only')

      // Only create the chunk message - AI response will be generated separately by frontend
      const chunkMessage = {
        id: 'chunk_content',
        role: 'system' as const,
        content: `📖 **Section ${chunkIndex + 1} of ${document.totalChunks}**\n\n${chunk.content}`,
        timestamp: new Date().toISOString()
      }

      conversationHistory = [chunkMessage]
      console.log('✅ Created initial conversation with chunk content only')
    }

    // Update current chunk if different (but don't reset session)
    if (progress.currentChunk !== chunkIndex) {
      await dbService.progress.update({
        where: {
          userId_documentId: {
            userId: session.user.id,
            documentId
          }
        },
        data: {
          currentChunk: chunkIndex,
          updatedAt: new Date() // Mark as recently accessed
        }
      })
    }

    // Return chat initialization data
    return NextResponse.json({
      success: true,
      sessionId: progress.sessionId,
      documentId,
      documentName: document.fileName,
      currentChunk: chunkIndex,
      chunkContent: chunk.content,
      totalChunks: document.totalChunks,
      messages: conversationHistory,
      hasExistingSession: conversationHistory.length > 2, // More than just chunk + AI intro
      progress: {
        current: chunkIndex + 1,
        total: document.totalChunks,
        percentage: Math.round(((chunkIndex + 1) / document.totalChunks) * 100)
      },
      navigation: {
        canGoNext: chunkIndex < document.totalChunks - 1,
        canGoPrevious: chunkIndex > 0
      }
    })

  } catch (error) {
    console.error('Chat initialization API error:', error)

    return NextResponse.json(
      {
        error: 'Failed to initialize chat session',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
