import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { dbService } from '@/lib/db-service'
import { BloomLevel, memoryService } from '@/lib/ai/memory-service'
import { fallbackManager } from '@/lib/ai/fallback-manager'
import { AIErrorHandler } from '@/lib/ai/error-handler'

/**
 * Streaming Chat API Endpoint
 * Handles real-time AI chat with Server-Sent Events streaming
 */

/**
 * Analyze if user is ready to proceed to next chunk
 * This is a simplified implementation - in production, this would use more sophisticated AI analysis
 */
async function analyzeUserReadiness(aiResponse: string, userMessage: string, _context: any): Promise<boolean> {
  // Simple heuristics for determining readiness
  const aiResponseLower = aiResponse.toLowerCase()
  const userMessageLower = userMessage.toLowerCase()

  // If AI is asking questions, user should engage more before proceeding
  if ((aiResponseLower.includes('?') && aiResponseLower.includes('what')) ||
    aiResponseLower.includes('how') || aiResponseLower.includes('why')) {
    return false
  }

  // If AI is providing explanations or summaries, user might be ready
  if (aiResponseLower.includes('great') || aiResponseLower.includes('excellent') ||
    aiResponseLower.includes('correct') || aiResponseLower.includes('well done')) {
    return true
  }

  // If user is asking clarifying questions, they're still learning
  if (userMessageLower.includes('?') || userMessageLower.includes('what') ||
    userMessageLower.includes('how') || userMessageLower.includes('explain')) {
    return false
  }

  // Default to allowing progression after some interaction
  return true
}

interface StreamChatRequest {
  documentId: string
  chunkIndex: number
  message: string
  provider?: 'openai' | 'anthropic'
  bloomLevel?: BloomLevel
}

/**
 * POST /api/chat/stream
 * Stream AI responses in real-time using Server-Sent Events
 */
export async function POST(request: NextRequest) {
  try {
    // Authenticate user
    const supabase = await createClient()
    const {
      data: { session },
    } = await supabase.auth.getSession()

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Parse request body
    const {
      documentId,
      chunkIndex,
      message,
      provider: _provider,
      bloomLevel
    }: StreamChatRequest = await request.json()

    // Validate required fields
    if (!documentId || chunkIndex === undefined || !message) {
      return NextResponse.json(
        { error: 'Missing required fields: documentId, chunkIndex, message' },
        { status: 400 }
      )
    }

    // Verify document exists and belongs to user
    const document = await dbService.document.findFirst({
      where: {
        id: documentId,
        userId: session.user.id
      }
    })

    if (!document) {
      return NextResponse.json(
        { error: 'Document not found or access denied' },
        { status: 404 }
      )
    }

    // Verify document is ready
    if (document.status !== 'READY') {
      return NextResponse.json(
        { error: 'Document is not ready for chat' },
        { status: 400 }
      )
    }

    // Get the current chunk
    const chunk = await dbService.chunk.findUnique({
      where: {
        documentId_chunkIndex: {
          documentId,
          chunkIndex
        }
      }
    })

    if (!chunk) {
      return NextResponse.json(
        { error: 'Chunk not found' },
        { status: 404 }
      )
    }

    // Get user's progress to find session ID
    const progress = await dbService.progress.findUnique({
      where: {
        userId_documentId: {
          userId: session.user.id,
          documentId
        }
      }
    })

    if (!progress?.sessionId) {
      return NextResponse.json(
        { error: 'AI session not initialized. Please initialize AI first.' },
        { status: 400 }
      )
    }

    // Ensure sessionId is not null (TypeScript safety)
    const sessionId = progress.sessionId
    if (!sessionId) {
      return NextResponse.json(
        { error: 'Invalid session ID' },
        { status: 400 }
      )
    }

    // Create streaming response
    const encoder = new TextEncoder()
    const stream = new ReadableStream({
      async start(controller) {
        try {
          // Send initial status
          controller.enqueue(
            encoder.encode(`data: ${JSON.stringify({
              type: 'status',
              content: 'AI is thinking...'
            })}\n\n`)
          )

          // Build conversation context
          const context = {
            userId: session.user.id,
            documentId,
            chunkIndex,
            chunkContent: chunk.content,
            sessionId, // Now guaranteed to be string, not null
            currentQuery: message,
            bloomLevel
          }

          console.log('🔍 Building educational context...')

          // Build educational context from memory
          const educationalContext = await memoryService.buildEducationalContext(
            documentId,
            session.user.id,
            message,
            chunk.content
          )

          console.log('✅ Educational context built successfully')

          // Prepare messages for AI
          const messages = [
            {
              role: 'system' as const,
              content: `You are an educational tutor using the Socratic method. Help the student understand the content through guided questions and explanations.

${educationalContext}

Remember to:
- Ask probing questions to check understanding
- Provide hints rather than direct answers
- Encourage critical thinking
- Build on previous learning from the context above`
            },
            {
              role: 'user' as const,
              content: message
            }
          ]

          // Generate streaming response directly with OpenAI
          let fullResponse = ''

          try {
            console.log('🌊 Starting direct OpenAI streaming...')

            // Send provider status
            controller.enqueue(
              encoder.encode(`data: ${JSON.stringify({
                type: 'provider',
                content: 'Using OpenAI Direct'
              })}\n\n`)
            )

            // Import OpenAI dynamically to avoid build issues
            const { default: OpenAI } = await import('openai')
            const openai = new OpenAI({
              apiKey: process.env.OPENAI_API_KEY
            })

            console.log('🚀 Creating OpenAI stream with messages...')

            // Create streaming completion
            const stream = await openai.chat.completions.create({
              model: process.env.OPENAI_MODEL || 'gpt-4o-mini',
              messages,
              stream: true,
              temperature: parseFloat(process.env.OPENAI_TEMPERATURE || '0.7'),
              max_tokens: parseInt(process.env.OPENAI_MAX_TOKENS || '1000')
            })

            console.log('✅ OpenAI stream created, processing tokens...')

            let tokenCount = 0
            for await (const chunk of stream) {
              const content = chunk.choices[0]?.delta?.content
              if (content) {
                fullResponse += content
                tokenCount++

                // Send each token immediately
                controller.enqueue(
                  encoder.encode(`data: ${JSON.stringify({
                    type: 'token',
                    content: content
                  })}\n\n`)
                )

                // Log every few tokens for debugging
                if (tokenCount % 5 === 0) {
                  console.log(`📝 Token ${tokenCount}: "${content.substring(0, 20)}..."`)
                }
              }
            }

            console.log(`✅ Direct streaming completed. Total tokens: ${tokenCount}, Response length: ${fullResponse.length}`)

            // Store conversation pair in memory asynchronously (don't block streaming)
            memoryService.addConversationPair(documentId, session.user.id, message, fullResponse, chunkIndex).catch(err =>
              console.warn('Failed to store conversation pair:', err)
            )

          } catch (error) {
            const aiError = AIErrorHandler.classifyError(error, fallbackManager.getCurrentProviderName())
            AIErrorHandler.logError(aiError, 'streaming chat')

            // Send user-friendly error message
            const userMessage = AIErrorHandler.getUserMessage(aiError)
            controller.enqueue(
              encoder.encode(`data: ${JSON.stringify({
                type: 'error',
                content: userMessage,
                technical: aiError.message,
                canRetry: aiError.retryable
              })}\n\n`)
            )

            // If we have a partial response, still try to complete
            if (fullResponse.length > 0) {
              controller.enqueue(
                encoder.encode(`data: ${JSON.stringify({
                  type: 'partial_complete',
                  content: fullResponse,
                  messageId: `msg_${Date.now()}`,
                  timestamp: new Date().toISOString(),
                  error: userMessage
                })}\n\n`)
              )
            }

            return // Exit early on error
          }

          // Analyze if user can proceed to next chunk
          // This is a simplified logic - in production, this would use more sophisticated AI analysis
          const canProceed = await analyzeUserReadiness(fullResponse, message, context)

          // Send completion signal with proceed status
          controller.enqueue(
            encoder.encode(`data: ${JSON.stringify({
              type: 'complete',
              content: fullResponse,
              messageId: `msg_${Date.now()}`,
              timestamp: new Date().toISOString(),
              canProceed
            })}\n\n`)
          )

          console.log(`✅ Streaming chat completed for document ${documentId}, chunk ${chunkIndex}`)

        } catch (error) {
          console.error('Streaming chat error:', error)

          // Send error to client
          controller.enqueue(
            encoder.encode(`data: ${JSON.stringify({
              type: 'error',
              content: error instanceof Error ? error.message : 'Unknown error occurred',
              timestamp: new Date().toISOString()
            })}\n\n`)
          )
        } finally {
          controller.close()
        }
      }
    })

    // Return streaming response with appropriate headers
    return new Response(stream, {
      headers: {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization'
      }
    })

  } catch (error) {
    console.error('Chat stream API error:', error)

    return NextResponse.json(
      {
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

/**
 * OPTIONS /api/chat/stream
 * Handle CORS preflight requests
 */
export async function OPTIONS() {
  return new Response(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization'
    }
  })
}
